# AI Studio 用户脚本监听修复

## 问题分析

通过对比您之前的 `userscripts/aistudio.user.js` 和当前的 TypeScript 实现，发现了以下关键问题：

### 1. 运行时机问题
- **原脚本**: 使用 `@run-at document-start`，在页面加载前就开始监听
- **新实现**: 在 DOM 加载完成后才初始化，错过了早期的网络请求

### 2. 拦截时机问题
- **原脚本**: 脚本加载时立即设置 XMLHttpRequest 拦截
- **新实现**: 在初始化完成后才启动拦截器，可能错过模型列表请求

### 3. 事件处理问题
- **原脚本**: 直接重写 `xhr.onload` 方法
- **新实现**: 需要更可靠的事件监听机制

## 修复方案

### 1. 早期拦截启动

在 `src/main.ts` 中修改了构造函数，添加了早期拦截启动：

```typescript
constructor() {
  // ... 其他初始化代码
  
  // 立即启动请求拦截器，在页面加载前就开始监听
  this.startEarlyInterception();
}

/**
 * 早期启动拦截器（在页面加载前）
 */
private startEarlyInterception(): void {
  logger.info('启动早期请求拦截器...');
  
  // 立即启动请求拦截器
  this.requestInterceptor.start();
  
  logger.info('早期请求拦截器已启动');
}
```

### 2. 模型数据同步机制

添加了模型数据准备就绪的 Promise 机制：

```typescript
private modelsDataReadyPromise: Promise<void>;
private resolveModelsDataPromise!: () => void;

constructor() {
  // 创建模型数据准备就绪的 Promise
  this.modelsDataReadyPromise = new Promise((resolve) => {
    this.resolveModelsDataPromise = resolve;
  });
  // ...
}

// 在模型加载事件中标记数据准备就绪
this.requestInterceptor.on('modelsLoaded', (models) => {
  this.handleModelsLoaded(models);
  // 标记模型数据已准备就绪
  this.resolveModelsDataPromise();
});
```

### 3. 改进的 XHR 拦截

在 `src/api/request-interceptor.ts` 中改进了 XHR 拦截逻辑：

```typescript
// 使用 addEventListener 而不是直接赋值
const handleLoad = function () {
  self.handleXHRResponse(xhr, url, body);
};

const handleProgress = function () {
  if (url.includes('GenerateContent')) {
    self.handleStreamResponse(xhr.responseText);
  }
};

xhr.addEventListener('load', handleLoad);
xhr.addEventListener('progress', handleProgress);

// 如果是 ListModels 请求，确保在 readyState 变化时也处理
if (url.includes('ListModels')) {
  const handleReadyStateChange = function () {
    if (xhr.readyState === 4 && xhr.status === 200) {
      self.handleXHRResponse(xhr, url, body);
    }
  };
  xhr.addEventListener('readystatechange', handleReadyStateChange);
}
```

### 4. 精确的 URL 匹配

改进了 URL 匹配逻辑，使用完整的 AI Studio API URLs：

```typescript
private shouldInterceptRequest(url: string): boolean {
  const listModelsUrl = 'https://alkalimakersuite-pa.clients6.google.com/$rpc/google.internal.alkali.applications.makersuite.v1.MakerSuiteService/ListModels';
  const generateContentUrl = 'https://alkalimakersuite-pa.clients6.google.com/$rpc/google.internal.alkali.applications.makersuite.v1.MakerSuiteService/GenerateContent';
  
  return url === listModelsUrl || url === generateContentUrl || 
         url.includes('ListModels') || url.includes('GenerateContent');
}
```

### 5. 初始化流程优化

修改了初始化流程，等待模型数据和 DOM 都准备好：

```typescript
// 等待模型数据和 DOM 都准备好
Promise.all([aiStudio2API['modelsDataReadyPromise'], domReadyPromise])
  .then(() => {
    logger.info('模型数据和 DOM 都已准备好，开始初始化应用...');
    return aiStudio2API.initialize();
  })
  .catch(error => {
    logger.error('应用初始化失败:', error);
  });
```

## 配置确认

确认 `vite.config.ts` 已正确配置：

```typescript
userscript: {
  // ...
  "run-at": "document-start",
  grant: [
    "GM_xmlhttpRequest",
    "GM_cookie",
    "GM_setValue",
    "GM_getValue",
    "GM_registerMenuCommand",
    "GM_setClipboard"
  ],
  connect: [
    "localhost",
    "alkalimakersuite-pa.clients6.google.com"
  ],
  noframes: true,
}
```

## 测试建议

1. **构建脚本**: `npm run build`
2. **安装脚本**: 将 `dist/gemini2api.user.js` 安装到 Tampermonkey
3. **访问页面**: 打开 `https://aistudio.google.com`
4. **检查日志**: 在浏览器控制台查看是否有 "拦截到模型列表" 的日志
5. **验证功能**: 确认调试面板显示模型列表

## 关键改进点

1. ✅ **在页面加载前启动拦截器**
2. ✅ **使用可靠的事件监听机制**
3. ✅ **精确匹配 AI Studio API URLs**
4. ✅ **同步模型数据和 DOM 初始化**
5. ✅ **保持与原脚本相同的运行时机**

这些修改确保了用户脚本能够像您之前的实现一样，在页面加载的早期阶段就开始监听网络请求，从而正确拦截到模型列表和其他 API 调用。
